# 酒馆人物预设项目

## 项目概述
这是一个用于AI聊天机器人的人物预设配置项目，主要包含AI女友聊天智能体的配置文件和相关的脚本处理工具。

## 项目结构

### 主要配置文件
- **夏瑾 V3.1 - Beta 1.json** - 主要的AI角色配置文件，包含完整的人物设定、对话模式和行为规则
- **包裹最新指示 (2).json** - 用户输入包装脚本，将用户输入包装在特定标签中
- **去掉中程思考.json** - 中程分析移除脚本，用于清理AI输出中的分析标签
- **移除cot和结束标签 (1).json** - 思维链和结束标签移除脚本，用于清理AI输出格式

## 文件功能详解

### 夏瑾 V3.1 - Beta 1.json
这是核心配置文件，包含以下主要模块：
- **基础配置**: AI模型选择、参数设置、温度控制等
- **提示词系统**: 包含多个可配置的提示词模块
- **角色设定**: 人物性格、背景、行为模式定义
- **对话控制**: 对话流程、POV视角、文风控制
- **NSFW控制**: 成人内容相关的配置和规则
- **世界观设定**: 故事背景和世界观构建规则

### 脚本配置文件
这些JSON文件是用于后处理AI输出的脚本配置：

1. **包裹最新指示 (2).json**
   - 功能：将用户输入包装在`<user_input>`标签中
   - 用途：帮助AI更好地识别和处理用户指令

2. **去掉中程思考.json**
   - 功能：移除AI输出中的`<中程分析>`标签及其内容
   - 用途：清理AI的中间思考过程，只保留最终输出

3. **移除cot和结束标签 (1).json**
   - 功能：移除思维链开始标签和`</正文已结束>`标签
   - 用途：清理AI输出格式，提供更清洁的对话体验

## 使用说明

1. **主配置文件**: 将`夏瑾 V3.1 - Beta 1.json`导入到支持的AI聊天平台
2. **脚本配置**: 根据需要启用相应的后处理脚本
3. **参数调整**: 根据使用需求调整温度、最大token等参数

## 注意事项

- 该配置主要针对中文对话优化
- 包含成人内容相关配置，请根据使用场景谨慎启用
- 建议在使用前根据具体需求调整相关参数和模块

## 文件版本说明

### 夏瑾 V3.1 - Beta 1.json (已清理为AI女友版)
- **用途**: AI女友聊天智能体
- **特点**: 移除了小说世界生成相关模块，专注于对话交流
- **清理内容**:
  - 移除复杂的世界观构建模块
  - 移除剧情推动和情节设计模块
  - 移除复杂的心理活动描述要求
  - 简化文风要求，更适合日常聊天
  - 保留核心的角色设定和对话控制功能

### 夏瑾 V3.1 - Beta 1 - 原版备份.json (如需要可手动创建)
- **用途**: 原始的小说世界生成智能体
- **特点**: 包含完整的小说创作功能模块

## 使用建议

### AI女友聊天模式
1. 使用清理后的 `夏瑾 V3.1 - Beta 1.json`
2. 专注于日常对话和情感交流
3. 避免复杂的剧情设定

### 小说创作模式
1. 如需小说创作功能，请备份并使用原版配置
2. 包含完整的世界观构建和剧情设计功能

## 更新日志

- V3.1 Beta 1: 当前版本，已清理为AI女友聊天专用版本
- 原版备份: 保留完整的小说世界生成功能
