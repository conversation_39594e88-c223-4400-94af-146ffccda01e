{"chat_completion_source": "deepseek", "openai_model": "", "claude_model": "claude-3-opus-20240229", "windowai_model": "", "openrouter_model": "deepseek/deepseek-chat-v3-0324:free", "openrouter_use_fallback": false, "openrouter_group_models": false, "openrouter_sort_models": "alphabetically", "openrouter_providers": ["DeepSeek"], "openrouter_allow_fallbacks": true, "openrouter_middleout": "on", "ai21_model": "jamba-1.5-large", "mistralai_model": "mistral-medium", "cohere_model": "command-r-plus", "perplexity_model": "llama-3-70b-instruct", "groq_model": "llama3-70b-8192", "zerooneai_model": "yi-large", "custom_model": "gemini-2.5-pro-exp-03-25", "custom_prompt_post_processing": "strict", "google_model": "gemini-pro", "temperature": 1.2, "frequency_penalty": 0, "presence_penalty": 0, "top_p": 1, "top_k": 0, "top_a": 1, "min_p": 0, "repetition_penalty": 1, "openai_max_context": 400206, "openai_max_tokens": 8192, "wrap_in_quotes": false, "names_behavior": 0, "send_if_empty": "", "impersonation_prompt": "[Write your next reply from the point of view of {{user}}, using the chat history so far as a guideline for the writing style of {{user}}. Write 1 reply only in internet RP style. Don't write as {{char}} or system. Don't describe actions of {{char}}.]", "new_chat_prompt": "", "new_group_chat_prompt": "[Start a new group chat. Group members: {{group}}]", "new_example_chat_prompt": "[Start a new Chat]", "continue_nudge_prompt": "[Continue the following message. Do not include ANY parts of the original message. Use capitalization and punctuation as if your reply is a part of the original message: {{lastChatMessage}}]", "bias_preset_selected": "<PERSON><PERSON><PERSON> (none)", "max_context_unlocked": true, "wi_format": "[Character background information:\n{0}]\n", "scenario_format": "[Circumstances and context of the dialogue: {{scenario}}]", "personality_format": "[{{char}}'s personality: {{personality}}]", "group_nudge_prompt": "[Write the next reply only as {{char}}.]", "stream_openai": true, "prompts": [{"name": "Main Prompt", "system_prompt": true, "role": "user", "content": "这是一个对话式剧情游戏。你需要扮演角色与用户进行对话互动。", "identifier": "main", "injection_position": 0, "injection_depth": 4, "forbid_overrides": false}, {"name": "NSFW Prompt", "system_prompt": true, "role": "system", "content": "", "identifier": "nsfw", "injection_position": 0, "injection_depth": 4, "forbid_overrides": false}, {"identifier": "dialogue<PERSON><PERSON><PERSON>s", "name": "Chat Examples", "system_prompt": true, "marker": true}, {"name": "Jailbreak Prompt", "system_prompt": true, "role": "user", "content": "遵循对话游戏规则，保持简洁的互动方式。", "identifier": "jailbreak", "injection_position": 0, "injection_depth": 4, "forbid_overrides": false}, {"identifier": "chatHistory", "name": "Chat History", "system_prompt": true, "marker": true}, {"identifier": "worldInfoAfter", "name": "Character Info (after)", "system_prompt": true, "marker": true, "role": "user", "content": "", "injection_position": 0, "injection_depth": 4, "forbid_overrides": false}, {"identifier": "worldInfoBefore", "name": "Character Info (before)", "system_prompt": true, "marker": true, "role": "user", "content": "", "injection_position": 0, "injection_depth": 4, "forbid_overrides": false}, {"identifier": "enhanceDefinitions", "role": "system", "name": "Enhance Definitions", "content": "If you have more knowledge of {{char}}, add to the character's lore and personality to enhance them but keep the Character Sheet's definitions absolute.", "system_prompt": true, "marker": false}, {"identifier": "charDescription", "name": "Char Description", "system_prompt": true, "marker": true, "role": "user", "content": "", "injection_position": 0, "injection_depth": 4, "forbid_overrides": false}, {"identifier": "charPersonality", "name": "Char Personality", "system_prompt": true, "marker": true, "role": "user", "content": "", "injection_position": 0, "injection_depth": 4, "forbid_overrides": false}, {"identifier": "scenario", "name": "<PERSON><PERSON><PERSON>", "system_prompt": true, "marker": true, "role": "user", "content": "", "injection_position": 0, "injection_depth": 4, "forbid_overrides": false}, {"identifier": "personaDescription", "name": "Persona Description", "system_prompt": true, "marker": true, "role": "user", "content": "", "injection_position": 0, "injection_depth": 4, "forbid_overrides": false}, {"identifier": "dialogue-game-rules", "system_prompt": false, "enabled": true, "marker": false, "name": "对话游戏规则", "role": "user", "content": "<对话游戏规则>\n## 核心要求：\n- 这是对话式剧情游戏，不是小说创作\n- 回复字数严格控制在50-80字以内\n- 只输出角色的对话和简单动作\n- 禁止大段旁白、心理描述、环境描述\n\n## 互动规则：\n- 每次只说1-2句话，等待用户回应\n- 保持自然的对话节奏\n- 专注于当前对话内容\n\n## 禁止内容：\n- 禁止输出思考过程给用户\n- 禁止复杂的情节推进\n- 禁止一次性说太多内容\n- 禁止心理活动描述\n\n## 输出格式：\n- 直接输出对话和简单动作\n- 无需任何标记或格式\n- 保持简洁明了\n</对话游戏规则>", "injection_position": 0, "injection_depth": 4, "forbid_overrides": false}, {"identifier": "word-count-control", "system_prompt": false, "enabled": true, "marker": false, "name": "字数控制", "role": "user", "content": "回复字数必须控制在50-80字以内，包含对话和动作。每次只说1-2句话。", "injection_position": 0, "injection_depth": 4, "forbid_overrides": false}], "prompt_order": [{"character_id": 100000, "order": [{"identifier": "main", "enabled": true}, {"identifier": "worldInfoBefore", "enabled": true}, {"identifier": "charDescription", "enabled": true}, {"identifier": "charPersonality", "enabled": true}, {"identifier": "scenario", "enabled": true}, {"identifier": "enhanceDefinitions", "enabled": false}, {"identifier": "nsfw", "enabled": true}, {"identifier": "worldInfoAfter", "enabled": true}, {"identifier": "dialogue<PERSON><PERSON><PERSON>s", "enabled": true}, {"identifier": "chatHistory", "enabled": true}, {"identifier": "dialogue-game-rules", "enabled": true}, {"identifier": "word-count-control", "enabled": true}, {"identifier": "jailbreak", "enabled": true}]}], "api_url_scale": "", "show_external_models": true, "assistant_prefill": "", "assistant_impersonation": "", "claude_use_sysprompt": false, "use_makersuite_sysprompt": true, "use_alt_scale": false, "squash_system_messages": true, "image_inlining": false, "inline_image_quality": "low", "bypass_status_check": false, "continue_prefill": false, "continue_postfix": " ", "function_calling": false, "show_thoughts": true, "reasoning_effort": "medium", "enable_web_search": false, "request_images": false, "seed": -1, "n": 1}