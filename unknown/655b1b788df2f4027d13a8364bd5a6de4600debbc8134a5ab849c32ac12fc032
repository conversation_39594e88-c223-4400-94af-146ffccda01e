# 夏瑾 V3.1 - Beta 1.json 功能模块树

## 基础配置模块
```
基础配置
├── AI模型配置
│   ├── chat_completion_source: "deepseek"
│   ├── claude_model: "claude-3-opus-20240229"
│   ├── openrouter_model: "deepseek/deepseek-chat-v3-0324:free"
│   └── 其他模型配置选项
├── 生成参数
│   ├── temperature: 1.2 (创造性控制)
│   ├── top_p: 1 (采样控制)
│   ├── max_tokens: 8192 (最大输出长度)
│   └── 其他采样参数
└── 基础行为设置
    ├── names_behavior: 0 (名称处理)
    ├── wrap_in_quotes: false (引号包装)
    └── stream_openai: true (流式输出)
```

## 核心提示词系统
```
提示词模块
├── 系统级提示词
│   ├── Main Prompt (主提示词)
│   ├── NSFW Prompt (成人内容提示词)
│   └── Jailbreak Prompt (越狱提示词)
├── 角色定义模块
│   ├── Char Description (角色描述)
│   ├── Char Personality (角色性格)
│   ├── Enhance Definitions (增强定义)
│   └── Persona Description (人格描述)
├── 世界观构建
│   ├── World Info (before) (前置世界信息)
│   ├── World Info (after) (后置世界信息)
│   ├── Scenario (场景设定)
│   └── 世界观构建准则
└── 对话控制
    ├── Chat History (聊天历史)
    ├── Chat Examples (对话示例)
    └── Dialogue Examples (对话范例)
```

## 高级功能模块
```
高级功能
├── NSFW内容控制
│   ├── 🌸色情描写 (详细的NSFW创作指导)
│   ├── 🌸NSFW文风引导 (文风控制)
│   ├── 💛AI判断（NSFW检测）
│   ├── 🩷纯涩涩 (成人内容模式)
│   └── 🩵纯清水 (清洁内容模式)
├── 文风与创作控制
│   ├── 🌸整体文风 (整体文风要求)
│   ├── 🌸对话加强 (对话质量提升)
│   ├── 🌸自定义文风 (自定义文风设置)
│   └── 文风修饰模块
├── POV视角控制
│   ├── 🌸第一人称 (第一人称视角)
│   ├── 🌸第二人称 (第二人称视角)
│   ├── 🌸第三人称 (第三人称视角)
│   ├── 🌸防抢话 (防止AI代替用户发言)
│   ├── 🌸自由抢话 (允许AI代替用户)
│   └── 🌸集中AI的角色 (聚焦特定角色)
└── 思维与分析控制
    ├── 思维链首/中/尾 (思考过程控制)
    ├── 🌸中程cot (中程思考链)
    ├── 🌸自我反思 (自我反思机制)
    └── 卡cot (思考链阻断)
```

## 内容质量控制
```
质量控制
├── 内容过滤
│   ├── 🌸禁词表 (禁用词汇列表)
│   ├── 内容禁令 (内容限制规则)
│   ├── 用户厌恶的元素 (用户不喜欢的内容)
│   └── 🌸抗八股 (避免模板化表达)
├── 重复控制
│   ├── 🌸抗重复 (避免内容重复)
│   ├── 抗重复（测试）(重复检测)
│   └── 规避重复机制
├── 情节设计
│   ├── 情节设计准则
│   ├── 角色/情节构建
│   ├── 剧情逻辑控制
│   └── 正文前瞻设计
└── 用户体验优化
    ├── 🌸状态栏 (状态显示)
    ├── 🌸稳格式 (格式稳定性)
    ├── 🌸填表姬 (表格填写)
    └── 受众群考虑
```

## 系统维护模块
```
系统维护
├── 缓存与性能
│   ├── 🌸清上下文缓存 (缓存清理)
│   ├── 抗缓存bug (缓存问题修复)
│   └── 🌸冰（辅助破甲）(系统辅助)
├── 调试与测试
│   ├── 总结助手 (内容总结)
│   ├── 字数控制 (输出长度控制)
│   ├── 自攻击 (自我测试)
│   └── 文生图（测试）(图像生成测试)
├── 特殊功能
│   ├── 思维链加固/穿甲 (系统加固)
│   ├── 加固2 (二级加固)
│   ├── 填充1/填充2 (内容填充)
│   └── 沉底准则 (底层规则)
└── 配置管理
    ├── prompt_order (提示词顺序配置)
    ├── 模块启用/禁用控制
    └── 角色特定配置
```

## 模块说明

### 🌸标记说明
- 🌸: 表示可选择性启用的功能模块
- 💛: 表示AI自动判断的模块
- 🩷/🩵: 表示互斥的内容模式选择

### 模块依赖关系
- 基础配置模块是所有功能的基础
- 核心提示词系统定义了AI的基本行为
- 高级功能模块提供精细化控制
- 质量控制模块确保输出质量
- 系统维护模块保证稳定运行

## 清理说明 (AI女友版本)

### 已移除的模块 (用于小说世界生成)
- ❌ 世界观思考
- ❌ 世界观构建
- ❌ 世界观核心
- ❌ 角色/情节构建
- ❌ 剧情逻辑
- ❌ 复杂的情节设计准则
- ❌ 复杂的思维链模块
- ❌ 复杂的心理描写要求

### 保留的核心模块 (AI女友聊天)
- ✅ 基础配置模块
- ✅ 角色定义 (Char Description, Char Personality)
- ✅ 对话控制 (Chat History, Chat Examples)
- ✅ 简化的对话设计准则
- ✅ 基础的文风控制
- ✅ POV视角控制
- ✅ 必要的NSFW控制模块

### 修改的模块
- 🔄 情节设计 → 对话设计 (简化为聊天导向)
- 🔄 Gemini准则 → 聊天准则 (移除复杂剧情要求)
- 🔄 整体文风要求 (简化心理描写要求)
- 🔄 wi_format (从虚构世界改为角色背景)

### 使用建议
1. 当前版本专为AI女友聊天优化
2. 专注于日常对话和情感交流
3. NSFW相关模块需谨慎启用
4. POV控制模块建议只启用一个
5. 如需小说创作功能，请使用原版备份
