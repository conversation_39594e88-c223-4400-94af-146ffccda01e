# 夏瑾 V3.1 - AI女友版 - 对话游戏配置说明

## 概述
这是专为对话式剧情游戏优化的AI女友配置文件，严格控制输出格式，确保简洁高效的互动体验。

## 核心特性

### 🎯 字数控制
- **严格限制**: 每次回复50-80字以内
- **包含内容**: 对话 + 简单动作
- **禁止内容**: 大段旁白、环境描述、心理活动

### 💬 对话规则
- **频次控制**: 每次只说1-2句话
- **互动节奏**: 说完等待用户回应
- **内容聚焦**: 专注当前对话主题

### 🚫 严格禁止
- ❌ 大段旁白和环境描述
- ❌ 复杂的心理活动描述  
- ❌ 一次性说太多话
- ❌ 输出思考过程给用户
- ❌ 复杂的情节推进

### ✅ 允许内容
- ✅ 角色对话
- ✅ 简单动作描述
- ✅ 基本情感表达
- ✅ 角色个性展现

## 配置结构

### 基础配置
```json
{
    "temperature": 1.2,
    "openai_max_tokens": 8192,
    "wi_format": "[Character background information:\n{0}]\n"
}
```

### 核心提示词模块

#### 1. 主提示词 (main)
- 定义这是对话式剧情游戏
- 设定基本交互规则

#### 2. 对话游戏规则 (dialogue-game-rules)
- 核心要求和互动规则
- 禁止内容和输出格式
- 字数和频次控制

#### 3. 字数控制 (word-count-control)
- 严格的字数限制
- 对话频次控制

#### 4. 角色定义模块
- charDescription: 角色描述
- charPersonality: 角色性格
- scenario: 场景设定

## 使用建议

### 适用场景
1. **对话式剧情游戏**: 需要简洁互动的游戏场景
2. **AI女友聊天**: 希望控制回复长度的日常对话
3. **角色扮演**: 需要保持角色一致性的RP场景

### 不适用场景
1. **小说创作**: 需要长篇描述的创作场景
2. **详细剧情**: 需要复杂情节推进的故事
3. **心理分析**: 需要深度心理描写的内容

### 调整建议
1. **字数调整**: 可在word-count-control模块中修改字数限制
2. **规则调整**: 可在dialogue-game-rules模块中修改具体规则
3. **角色调整**: 在角色定义模块中设定具体的角色信息

## 与其他版本的区别

| 特性 | 对话游戏版 | 标准AI女友版 | 原版 |
|------|------------|--------------|------|
| 字数控制 | 50-80字 | 无严格限制 | 1000+字 |
| 输出内容 | 对话+动作 | 对话+描述 | 小说式 |
| 心理描写 | 禁止 | 简化 | 详细 |
| 互动频次 | 1-2句话 | 较自由 | 长篇 |
| 适用场景 | 对话游戏 | 日常聊天 | 小说创作 |

## 技术细节

### 启用的模块
- main: 主提示词
- charDescription: 角色描述
- charPersonality: 角色性格
- scenario: 场景设定
- dialogue-game-rules: 对话游戏规则
- word-count-control: 字数控制
- chatHistory: 聊天历史
- dialogueExamples: 对话示例

### 禁用的模块
- 所有复杂的世界观构建模块
- 所有小说创作相关模块
- 所有复杂的心理分析模块
- 所有长篇输出控制模块

## 故障排除

### 常见问题
1. **回复太长**: 检查word-count-control模块是否启用
2. **包含旁白**: 检查dialogue-game-rules模块设置
3. **一次说太多**: 确认互动规则配置正确

### 调试建议
1. 确保prompt_order中的模块顺序正确
2. 检查各模块的enabled状态
3. 验证角色设定是否符合对话游戏需求

## 更新日志
- V3.1 对话游戏版: 初始版本，专为对话游戏优化
- 基于夏瑾 V3.1 Beta 1，移除所有小说创作功能
- 新增严格的字数和格式控制
